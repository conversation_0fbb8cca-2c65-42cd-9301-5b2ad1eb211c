<template>
  <a-drawer :width="1000" title="订单详情" :open="visible" @close="handleClose">
    <template #extra>
      <a-space>
        <a-button :loading="loading" :disabled="currentIdx === 0" @click="handleChangeInfo('prev')">上一条</a-button>
        <a-button :loading="loading" :disabled="currentIdx === tableIds.length - 1" @click="handleChangeInfo('next')">下一条</a-button>
      </a-space>
    </template>
    <a-spin :spinning="loading">
      <!-- <div class="pl-12 pt-20 pb-20 color-#FF8D1A bg-#FCF6EC mb-20 text-20 font-600">订单已取消</div> -->
      <!-- <div class="pl-12 pt-20 pb-20 bg-#FEF0F0 mb-20">
        <div class="color-#EB1237 text-20 font-600 mb-6">订单异常</div>
        <div class="color-#666">异常原因：商品编码缺失</div>
      </div> -->
      <div class="px-10% mb-16px">
        <a-steps :current="selectStep" :items="stepList" size="small"></a-steps>
      </div>
      <Form />
      <div class="drawer-title">订单商品</div>
      <vxe-table
        border
        size="small"
        :data="details.storeList"
        class="!text-12"
        ref="tableRef"
        :merge-cells="mergeCells"
        :column-config="{
          resizable: true,
        }"
        :row-config="{ isHover: true, height: 72 }"
      >
        <vxe-column type="seq" width="50" align="center"></vxe-column>
        <vxe-column width="60" field="name" title="主图">
          <template #default="{ row }">
            <BaseImage :width="60" :height="60" :src="row.image_url"></BaseImage>
          </template>
        </vxe-column>
        <vxe-column width="150" field="name" title="商品名称" :formatter="defaultFormatter"></vxe-column>
        <vxe-column width="150" field="aaa" title="平台商品编码" :formatter="defaultFormatter"></vxe-column>
        <vxe-column width="150" field="aaa" title="规格型号" :formatter="defaultFormatter"></vxe-column>
        <vxe-column width="150" field="aaa" title="所属供应商" :formatter="defaultFormatter"></vxe-column>
        <vxe-column width="150" field="aaa" title="订单数量" :formatter="defaultFormatter"></vxe-column>
        <vxe-column width="150" field="aaa" title="供货价" :formatter="defaultFormatter"></vxe-column>
        <vxe-column width="150" field="aaa" title="合计金额" :formatter="priceFormatter"></vxe-column>
        <vxe-column width="150" field="aaa" title="可用库存" :formatter="defaultFormatter"></vxe-column>
        <vxe-column width="150" field="remark" title="备注" :formatter="defaultFormatter"></vxe-column>
      </vxe-table>
      <div class="flex justify-end mt-20 mb-20">
        <div class="c-#999 w-200 flex flex-col gap-4 lh-20px">
          <div class="flex justify-between">
            <span>商品总数量:</span>
            <span>{{ countTotal }}</span>
          </div>
          <div class="flex justify-between text-12px c-#333 font-600">
            <span>订单总金额：</span>
            <span>¥{{ new Decimal(countTotalPrice).plus(countOtherPrice).toFixed(2) }}</span>
          </div>
        </div>
      </div>
    </a-spin>
  </a-drawer>
  <a-modal v-model:open="openModal" title="附件" :footer="null">
    <div class="min-h-[250px]">
      <div v-for="item in details.files" :key="item.id" class="cursor-pointer">
        <a-space class="cursor-point" @click="previewAuthFile(item)">
          <LinkOutlined />
          <span class="text-blue-500 font-size-14">{{ item.name }}</span>
        </a-space>
      </div>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { useBaseForm, type BaseFormItem } from '@/hook/useBaseForm'
// import { getOrderDetail, getStoreDetailList, getReturnDetailList } from '@/servers/OrderManage'
import { message } from 'ant-design-vue'
import Decimal from 'decimal.js'
import { withDirectives } from 'vue'
import { vCopy } from '@/directive/copy'
import { defaultFormatter, priceFormatter } from '@/utils/VxeUi'
import { VxeTable, VxeTablePropTypes } from 'vxe-table'
import BaseImage from '@/components/BaseImage/index.vue'
import { CopyOutlined, LinkOutlined } from '@ant-design/icons-vue'
// import OrderPurchaseItem from './OrderPurchaseItem.vue'

interface AttachmentFileItem {
  id: number | string
  name: string
}
const VITE_APP_ENV = ref<string | undefined>(import.meta.env.VITE_APP_ENV)
const userData = ref<Record<string, any>>(JSON.parse(localStorage.getItem('userData') || '{}'))

const currentIdx = computed(() => tableIds.value.findIndex((id) => id === selectId.value))

const visible = ref(false)
// 加载状态
const loading = ref(false)
// 当前选中的id
const selectId = ref()
// 合并单元格
const mergeCells = ref<VxeTablePropTypes.MergeCells>([])
const tableRef = useTemplateRef<InstanceType<typeof VxeTable>>('tableRef')
const tableIds = ref<number[]>([])
const openModal = ref(false)
// 表单
const details = ref<any>({
  storeList: [],
  returnList: [],
  purchaseOrderDetails: [],
  files: [
    {
      id: 123,
      name: '测试',
    },
  ],
})
// 当前步骤条
const selectStep = ref(0)
// 步骤条列表
const stepList = [
  {
    title: '待付款',
  },
  {
    title: '已付款待审核',
  },
  {
    title: '待财审',
  },
  {
    title: '发货中',
  },
  {
    title: '已发货',
  },
  {
    title: '已签收',
  },
  {
    title: '已完成',
  },
]

// 计算商品总数量
const countTotal = computed(() => {
  return new Decimal(details.value.purchaseOrderDetails.reduce((acc, curr) => acc + (curr.purchase_quantity || 0), 0)).toNumber()
})

// 计算商品总价
const countTotalPrice = computed(() => {
  return new Decimal(details.value.purchaseOrderDetails.reduce((acc, curr) => acc + (curr.purchase_tax_price || 0) * (curr.purchase_quantity || 0), 0)).toFixed(2)
})

// 计算其他费用
const countOtherPrice = computed(() => {
  return new Decimal(details.value.purchaseOrderDetails.reduce((acc, curr) => acc + (curr.other_fees || 0), 0)).toFixed(2)
})

// 获取详情
const getDetail = (id: number) => {
  const params = { id }
  loading.value = true
  /* Promise.all([getOrderDetail(params), getStoreDetailList(params), getReturnDetailList(params)])
    .then(([basicRes, storeRes, returnRes]) => {
      if (basicRes.data.order_status === 2) {
        selectStep.value = 2
      } else if ([20, 30].includes(basicRes.data.shipment_status)) {
        selectStep.value = 1
      } else {
        selectStep.value = 0
      }
      basicRes.data.shipments_address = `${basicRes.data.province}${basicRes.data.city}${basicRes.data.area}${basicRes.data.shipments_address}`
      details.value = {
        ...basicRes.data,
        storeList: mergeTableCells(storeRes.data),
        returnList: returnRes.data,
      }
    })
    .finally(() => {
      loading.value = false
    }) */
}

// 合并
const mergeTableCells = (data: any) => {
  const columns = tableRef.value?.getColumns() || []
  const mergeList: any[] = []
  mergeCells.value = []
  let findIndex = 0
  const list = data.flatMap((item: any, index: number) => {
    if (item.purchase_in_relation_infos?.length === 0) {
      return item
    }
    if (item.purchase_in_relation_infos?.length > 1) {
      // 为每个需要合并的列创建合并规则
      columns.forEach((col, colIndex) => {
        const mergeKeys = ['io_id', 'purchase_inbound_quantity', 'inbound_time']
        if (!mergeKeys.includes(col.field)) {
          mergeList.push({
            row: index + findIndex,
            col: colIndex,
            rowspan: item.purchase_in_relation_infos.length,
            colspan: 1,
          })
        }
      })
      findIndex += item.purchase_in_relation_infos.length - 1
    }
    return item.purchase_in_relation_infos.map((product: any) => {
      return {
        ...item,
        ...product,
        id: product.io_id,
      }
    })
  })
  nextTick(() => {
    mergeCells.value = mergeList
  })
  return list
}

// 关闭
const handleClose = () => {
  visible.value = false
}
// 显示
const handleShow = (id: number, ids: number[]) => {
  selectId.value = id
  visible.value = true
  tableIds.value = ids
  getDetail(id)
}

// 上一条
const handleChangeInfo = (type: 'prev' | 'next') => {
  const index = tableIds.value.findIndex((id) => id === selectId.value)
  if (index === 0 && type === 'prev') {
    message.error('已经是第一条')
    return
  }
  if (index === tableIds.value.length - 1 && type === 'next') {
    message.error('已经是最后一条')
    return
  }

  if (type === 'prev') {
    selectId.value = tableIds.value[index - 1]
  } else {
    selectId.value = tableIds.value[index + 1]
  }
  getDetail(selectId.value)
}

const formArr = ref<BaseFormItem[]>([
  {
    type: 'title',
    label: '交易信息',
  },
  {
    label: () => h('div', {}, [h('span', {}, '聚水潭订单编号:'), details.value.number && withDirectives(h(CopyOutlined, { class: 'ml-4 c-primary cursor-pointer' }), [[vCopy, details.value.number]])]),
    key: 'number',
    type: 'text',
    span: 6,
  },
  {
    label: () => h('div', {}, [h('span', {}, '线上订单编号:'), details.value.number && withDirectives(h(CopyOutlined, { class: 'ml-4 c-primary cursor-pointer' }), [[vCopy, details.value.number]])]),
    key: 'number',
    type: 'text',
    span: 6,
  },
  {
    label: '下单时间:',
    key: 'create_at',
    type: 'text',
    span: 6,
  },
  {
    label: '付款时间:',
    key: 'pay_time',
    type: 'text',
    span: 6,
  },
  {
    label: '完成时间:',
    key: 'complete_time',
    type: 'text',
    span: 6,
  },
  {
    label: '买家昵称:',
    key: 'name',
    type: 'text',
    span: 6,
  },
  {
    label: '买家留言:',
    key: 'buyer_remark',
    type: 'text',
    span: 6,
  },
  {
    label: '卖家备注:',
    key: 'seller_remark',
    type: 'text',
    span: 6,
  },
  {
    label: '收货信息',
    type: 'title',
  },
  {
    label: '收货人:',
    key: 'consignee',
    type: 'text',
    span: 6,
  },
  {
    label: '联系电话:',
    key: 'phone_number',
    type: 'text',
    span: 6,
  },
  {
    label: '收货地址:',
    key: 'shipments_address',
    type: 'text',
    span: 6,
  },
  {
    label: '邮编:',
    key: 'postal_code',
    type: 'text',
    span: 6,
  },
  {
    label: '配送信息',
    type: 'title',
  },
  {
    label: '快递公司:',
    key: 'aaa',
    type: 'text',
    span: 6,
  },
  {
    label: '快递单号:',
    key: 'aaa',
    type: 'text',
    span: 6,
  },
  {
    label: '箱唛（面单）:',
    key: 'aaa',
    type: 'slot',
    span: 6,
    slots: () => h('div', { class: 'c-primary cursor-pointer', onClick: () => onClickFiles('aaa') }, '查看附件'),
  },
  {
    label: '条形码（标签）:',
    key: 'aaa',
    type: 'slot',
    span: 6,
    slots: () => h('div', { class: 'c-primary cursor-pointer', onClick: () => onClickFiles('aaa') }, '查看附件'),
  },
  {
    label: '其他附件:',
    key: 'aaa',
    type: 'slot',
    span: 6,
    slots: () => h('div', { class: 'c-primary cursor-pointer', onClick: () => onClickFiles('aaa') }, '查看附件'),
  },
  {
    label: '发货区域:',
    key: 'aaa',
    type: 'text',
    span: 6,
  },
  {
    label: '发货仓库:',
    key: 'aaa',
    type: 'text',
    span: 6,
  },
  {
    label: '交接单:',
    key: 'aaa',
    type: 'slot',
    span: 6,
    slots: () => h('div', { class: 'c-primary cursor-pointer', onClick: () => onClickFiles('aaa') }, '查看附件'),
  },
])

const onClickFiles = (key: string) => {
  console.log(key)
  openModal.value = true
}

const previewAuthFile = async (files: AttachmentFileItem) => {
  const fileId = files.id
  if (!fileId) {
    message.warning('文件不存在')
    return
  }
  try {
    let url = ''
    if (VITE_APP_ENV.value == 'development') {
      url = `${window.location.origin}/api/api/Files/ViewByFileId?fileId=${fileId}`
    } else {
      url = `${window.location.origin}/api/Files/ViewByFileId?fileId=${fileId}`
    }

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/octet-stream',
        logintoken: (userData.value as any).login_token,
      },
    })
    if (!response.ok) {
      message.warning('获取授权书失败')
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const blob = await response.blob()
    const previewUrl = URL.createObjectURL(blob)
    window.open(previewUrl, '_blank')
    setTimeout(() => URL.revokeObjectURL(previewUrl), 30000)
  } catch (error) {
    console.error('授权书预览失败:', error)
    // message.error('授权书预览失败')
  }
}

const [Form] = useBaseForm({
  formConfig: formArr,
  modelValue: details,
  isText: true,
})

defineExpose({
  show: handleShow,
})
</script>

<style scoped lang="scss">
:deep(.drawer-title) {
  position: relative;

  &::after {
    position: absolute;
    top: 50%;
    left: 0;
    width: 2px;
    height: 12px;
    content: '';
    background-color: #1890ff;
    transform: translateY(-50%);
  }
}

:deep(.ant-form) {
  color: red !important;

  .ant-form-item {
    .ant-form-item-label {
      label {
        color: #999 !important;
      }
    }

    .ant-form-item-control {
      .a-text-wrap {
        color: #333;
      }
    }
  }
}
</style>
