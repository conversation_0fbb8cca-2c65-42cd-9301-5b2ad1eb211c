<template>
  <a-select
    :getPopupContainer="(triggerNode: any) => triggerNode.parentNode"
    v-model:value="item.value"
    :placeholder="item.placeholder || '请输入供应商ID或编码搜索'"
    allow-clear
    show-search
    :filter-option="false"
    :loading="loading"
    :options="supplierOptions"
    class="w-200px"
    @search="handleSearch"
    @change="handleChange"
    v-bind="item"
  >
    <template #notFoundContent>
      <div v-if="loading" class="text-center py-2">
        <a-spin size="small" />
        <span class="ml-2">搜索中...</span>
      </div>
      <div v-else-if="searchValue && supplierOptions.length === 0" class="text-center py-2 text-gray-500">
        未找到相关供应商
      </div>
      <div v-else class="text-center py-2 text-gray-500">
        请输入供应商ID或编码进行搜索
      </div>
    </template>
  </a-select>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from 'vue'
import { GetSupplierDropdownData } from '@/servers/Supplier'
import { FormItemType } from '../type'
import { debounce } from 'lodash-es'

defineEmits<{
  (e: 'search'): void
}>()

const item = defineModel<FormItemType<'supplier-search'>>('item', { required: true })

// 状态管理
const loading = ref(false)
const searchValue = ref('')
const supplierOptions = ref<Array<{ label: string; value: any }>>([])

// 供应商数据缓存
const allSuppliers = ref<Array<{
  supplier_id: number
  supplier_name: string
  supplier_number: string
}>>([])

// 加载所有供应商数据
const loadAllSuppliers = async () => {
  if (allSuppliers.value.length > 0) return // 已加载过就不重复加载

  loading.value = true
  try {
    const res = await GetSupplierDropdownData()
    if (res.success && res.data) {
      allSuppliers.value = res.data
    }
  } catch (error) {
    console.error('加载供应商数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 搜索供应商
const searchSuppliers = (keyword: string) => {
  if (!keyword || keyword.trim().length < 1) {
    supplierOptions.value = []
    return
  }

  const searchTerm = keyword.trim().toLowerCase()

  // 在本地数据中搜索，支持通过ID、编码或名称搜索
  const filteredSuppliers = allSuppliers.value.filter((supplier) => {
    return (
      supplier.supplier_id.toString().includes(searchTerm) ||
      supplier.supplier_number.toLowerCase().includes(searchTerm) ||
      supplier.supplier_name.toLowerCase().includes(searchTerm)
    )
  })

  // 转换为选项格式
  supplierOptions.value = filteredSuppliers.map((supplier) => ({
    label: `${supplier.supplier_name} (${supplier.supplier_number})`,
    value: supplier.supplier_id
  }))
}

// 防抖搜索
const debouncedSearch = debounce(searchSuppliers, 300)

// 处理搜索输入
const handleSearch = async (value: string) => {
  searchValue.value = value

  // 如果还没有加载数据，先加载
  if (allSuppliers.value.length === 0) {
    await loadAllSuppliers()
  }

  debouncedSearch(value)
}

// 处理选择变化
const handleChange = (value: any) => {
  // 选择变化时的处理逻辑
  console.log('选中的供应商ID:', value)
}

// 初始化时如果有值，需要加载对应的选项
watch(() => item.value.value, async (newValue) => {
  if (newValue && allSuppliers.value.length === 0) {
    // 如果有初始值但没有数据，先加载数据
    await loadAllSuppliers()
  }

  if (newValue && allSuppliers.value.length > 0) {
    // 根据ID查找对应的供应商
    const supplier = allSuppliers.value.find(s => s.supplier_id === newValue)
    if (supplier) {
      supplierOptions.value = [{
        label: `${supplier.supplier_name} (${supplier.supplier_number})`,
        value: supplier.supplier_id
      }]
    }
  }
}, { immediate: true })

// 组件挂载时预加载数据
onMounted(() => {
  loadAllSuppliers()
})
</script>

<style scoped>
.w-200px {
  width: 200px;
}
</style>
