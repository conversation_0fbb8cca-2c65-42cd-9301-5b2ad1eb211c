<template>
  <div class="main">
    <!-- 筛选表单 -->
    <SearchForm ref="formRef" v-model:form="formArr" :page-type="PageType.ORDER_LIST" @search="search" @setting="tableRef?.showTableSetting()" />

    <!-- 表格 -->
    <BaseTable ref="tableRef" :isCheckbox="true" :pageType="PageType.ORDER_LIST" :get-list="getListFn" v-model:form="formArr" :isIndex="true" :form-format="formFormat" :data-format="dataFormat" :footer-data="footerData" :show-footer="showFooter">
      <!-- 左侧按钮 -->
      <template #left-btn>
        <!-- 可以在这里添加导出等功能按钮 -->
      </template>

      <!-- 主图列 -->
      <template #order_items="{ row, column }">
        <component v-if="row.order_items && row.order_items.length > 0" :is="orderItemsListRender({ column, row, rowHeight: 50 })"></component>
        <div v-else class="order-items-empty">
          <img src="/src/assets/icons/error-image.svg" alt="默认图片" class="default-image" />
        </div>
      </template>

      <!-- 收货信息列 -->
      <template #shipping_info="{ row }">
        <div v-if="row.shipping_info" class="shipping-info">
          <div>收货人：{{ row.shipping_info.recipient || '--' }}</div>
          <div>手机号：{{ formatPhone(row.shipping_info.telephone) || '--' }}</div>
          <div>收货地址：{{ row.shipping_info.address?.address_info || '--' }}</div>
        </div>
        <span v-else>--</span>
      </template>

      <!-- 操作列 -->
      <template #operate="{ row }">
        <a-button type="text" @click="viewDetail(row)">查看</a-button>
      </template>
    </BaseTable>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, h } from 'vue'
import { message } from 'ant-design-vue'

import BaseTable from '@/components/BaseTable/index.vue'
import SearchForm from '@/components/SearchForm/index.vue'
import { imageListRender } from '@/utils/VxeRender'
import DivGrid from '@/components/EasyTable/DivGrid.vue'
import { PageType } from '@/common/enum'
import { GetOrderList } from '@/servers/OrderManage'
import dayjs from 'dayjs'
import { getCommonOption } from '@/utils'

// 表格引用
const tableRef = ref()
const formRef = ref()

// 合计行数据
const footerData = ref([{ seq: '合计', total_amount: '0.00' }])
const showFooter = ref(true)

// 订单商品表格列配置
const orderItemColumns = ref([
  {
    title: '序号',
    field: 'index',
    width: 60,
    align: 'center',
  },
  {
    title: '主图',
    field: 'main_images_id',
    width: 80,
    align: 'center',
    cellRender: { name: 'imageById' },
  },
  {
    title: '商品名称',
    field: 'product_name',
    width: 300,
    align: 'left',
  },
  {
    title: '所属供应商',
    field: 'supplier',
    width: 200,
    align: 'center',
  },
  {
    title: '平台商品编码',
    field: 'product_number',
    width: 150,
    align: 'left',
    cellRender: {
      name: 'copy',
    },
  },
  {
    title: '规格型号',
    field: 'spec_info',
    width: 120,
    align: 'left',
  },
  {
    title: '数量',
    field: 'qty',
    width: 80,
    align: 'right',
  },
  {
    title: '供货价',
    field: 'store_supply_price',
    width: 100,
    align: 'right',
  },
  {
    title: '合计金额',
    field: 'total_amount',
    width: 100,
    align: 'right',
  },
  {
    title: '可用库存',
    field: 'inventory_num',
    width: 100,
    align: 'right',
  },
])

// 筛选表单配置
const formArr = ref([
  {
    key: 'order_number',
    label: '订单编号',
    type: 'batch-input',
    value: '',
    isShow: true,
    placeholder: '请输入订单编号，回车批量筛选',
  },

  {
    key: 'product_number',
    label: '平台商品编号',
    type: 'batch-input',
    value: '',
    isShow: true,
    placeholder: '请输入平台商品编号，回车批量筛选',
  },
  {
    key: 'supplier_name_or_number',
    label: '供应商名称/编号',
    type: 'supplier-search',
    value: '',
    isShow: true,
    placeholder: '请输入供应商ID或编码搜索',
    class: 'w-200',
  },
  {
    key: 'product_name',
    label: '商品名称',
    type: 'input',
    value: '',
    isShow: true,
    placeholder: '请输入商品名称',
  },
  {
    key: 'question_type',
    label: '异常原因',
    type: 'input',
    value: '',
    isShow: true,
    placeholder: '请输入异常原因',
  },
  {
    key: 'buyer_account',
    label: '买家账号',
    type: 'input',
    value: '',
    isShow: true,
    placeholder: '请输入买家账号',
  },
  {
    key: 'tracking_number',
    label: '物流单号',
    type: 'input',
    value: '',
    isShow: true,
    placeholder: '请输入物流单号',
  },

  {
    key: 'order_create_at',
    label: '下单时间',
    type: 'range-picker',
    value: [],
    isShow: true,
    placeholder: ['下单开始时间', '下单结束时间'],
    formKeys: ['order_create_at_start', 'order_create_at_end'],
    valueFormat: 'YYYY-MM-DD HH:mm:ss',
  },

  {
    key: 'pay_date',
    label: '付款时间',
    type: 'range-picker',
    value: [],
    isShow: true,
    placeholder: ['付款开始时间', '付款结束时间'],
    formKeys: ['pay_date_start', 'pay_date_end'],
    valueFormat: 'YYYY-MM-DD HH:mm:ss',
  },

  {
    key: 'finish_date',
    label: '完成时间',
    type: 'range-picker',
    value: [],
    isShow: true,
    placeholder: ['完成开始时间', '完成结束时间'],
    formKeys: ['finish_date_start', 'finish_date_end'],
    valueFormat: 'YYYY-MM-DD HH:mm:ss',
  },
  {
    key: 'order_status',
    label: '订单状态',
    type: '',
    value: '',
    isQuicks: true,
    isShow: true,
    placeholder: '请选择订单状态',
    options: [],
  },
  {
    key: 'exception_status',
    label: '异常状态',
    type: '',
    isQuicks: true,
    value: '',
    isShow: true,
    placeholder: '请输入错误状态',
    options: [],
  },
])

// 搜索
const search = () => {
  tableRef.value?.search()
}

// 表单格式化
const formFormat = (params: any) => {
  const formattedParams = {
    ...params,
    is_page: true,
    is_get_total: true,
    is_get_total_only: false,
  }

  // 处理日期范围参数
  formArr.value.forEach((item: any) => {
    if (item.formKeys && item.value && Array.isArray(item.value) && item.value.length === 2) {
      formattedParams[item.formKeys[0]] = item.value[0]
      formattedParams[item.formKeys[1]] = item.value[1]
    }
  })

  return formattedParams
}

// 数据格式化
const dataFormat = (data: any[]) => {
  return data.map((item: any) => ({
    ...item,
    // 格式化时间显示
    order_create_at: item.order_create_at ? dayjs(item.order_create_at).format('YYYY-MM-DD HH:mm:ss') : '--',
    pay_date: item.pay_date ? dayjs(item.pay_date).format('YYYY-MM-DD HH:mm:ss') : '--',
    finish_date: item.finish_date ? dayjs(item.finish_date).format('YYYY-MM-DD HH:mm:ss') : '--',
  }))
}

// 获取列表数据
const getListFn = async (params: any) => {
  // 添加获取合计数据的参数
  params.is_get_total = true

  const res = await GetOrderList(params)

  // 计算合计金额 - 统计订单级别的total_amount
  if (res?.data?.list && Array.isArray(res.data.list)) {
    let totalAmount = 0
    res.data.list.forEach((order: any) => {
      totalAmount += Number(order.total_amount || 0)
    })

    // 更新合计行数据
    footerData.value = [{ seq: '合计', total_amount: totalAmount.toFixed(2) }]
    showFooter.value = true
  }

  return res
}

// 格式化手机号
const formatPhone = (phone: string) => {
  if (!phone) return '--'
  if (phone.length <= 7) return phone
  return phone.replace(/(\d{3})\d*(\d{4})/, '$1****$2')
}

// 订单商品列表渲染器
const orderItemsListRender = ({ column, row, rowHeight }: any) => {
  // 准备订单商品数据，添加序号和图片URL
  const orderItemsData = (row.order_items || []).map((item: any, index: number) => ({
    ...item,
    index: index + 1,
    main_image_url: null, // 将在DivGrid中处理
    spec_info: item.spec_info || '--',
    store_supply_price: item.store_supply_price || 0,
    total_amount: item.total_amount || 0,
    inventory_num: item.inventory_num || 0,
  }))

  return imageListRender(null, {
    key: 'order_items',
    column,
    row,
    size: rowHeight || 50,
    options: {
      width: '60vw',
      render: ({ maxHeight }: any) =>
        h(
          'div',
          { class: 'overflow-auto' },
          h(DivGrid, {
            columns: orderItemColumns.value,
            data: orderItemsData,
            rowHeight: 46,
            height: Math.min(maxHeight, orderItemsData.length * 46 + 30),
            border: true,
            keyField: 'id',
          }),
        ),
    },
  })
}

// 查看详情
const viewDetail = (row: any) => {
  // TODO: 实现查看详情功能
  message.info('查看详情功能待实现')
}
// 获取订单状态和异常状态选项
const getOrderOptions = async () => {
  try {
    // 获取订单状态(28)和异常状态(29)的选项数据
    const [orderStatusOptions, exceptionStatusOptions] = await getCommonOption([28, 29])

    formArr.value.forEach((item: any) => {
      if (item.key === 'order_status') {
        // 订单状态下拉数据
        item.options = orderStatusOptions || []
      }
      if (item.key === 'exception_status') {
        // 异常状态下拉数据
        item.options = exceptionStatusOptions || []
      }
    })
  } catch (error) {
    console.error('获取订单选项失败:', error)
  }
}

// 注意：供应商搜索功能已移至FormSupplierSearch组件内部处理

onMounted(() => {
  // 页面加载时获取选项数据并自动搜索
  getOrderOptions()
  // 页面加载时自动搜索
  search()
})
</script>

<style scoped lang="scss">
.main {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.shipping-info {
  font-size: 12px;
  line-height: 1.4;

  div {
    margin-bottom: 2px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.order-items-empty {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 40px;

  .default-image {
    width: 40px;
    height: 40px;
    object-fit: cover;
    border-radius: 4px;

    // border: 1px solid #d9d9d9;
  }
}

// 复制按钮样式
:deep(.copy-cell) {
  &:hover .copy-cell-btn {
    display: inline-flex !important;
    align-items: center;
    justify-content: center;
  }

  .copy-cell-btn {
    display: none;
    cursor: pointer;

    &:hover {
      color: #40a9ff;
    }
  }
}
</style>
